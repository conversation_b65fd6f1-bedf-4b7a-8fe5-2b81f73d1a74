<?php
declare(strict_types=1);
/**
 * Copyright © Coditron Technologies All rights reserved.
 * See COPYING.txt for license details.
 * http://www.coditron.com | <EMAIL>
 */

namespace Coditron\CustomShippingRate\Helper;

use AllowDynamicProperties;
use Magento\Framework\App\Cache\ManagerFactory;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\Encryption\EncryptorInterface;
use Magento\Store\Model\ScopeInterface;
use Coditron\CustomShippingRate\Model\Carrier;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\App\Config\Storage\WriterInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use GuzzleHttp\Client;
use GuzzleHttp\ClientFactory;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Psr7\ResponseFactory;
use Magento\Framework\Webapi\Rest\Request;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory as MpSellerShipRateCollection;
use Webkul\Marketplace\Helper\Data as DataAlias;

/***
 * @deprecated
 * Class Data
 * @package Coditron\CustomShippingRate\Helper
 */
#[AllowDynamicProperties]
class Data extends AbstractHelper
{
    /**
     * @var array
     */
    protected $shippingType;

    /**
     * @var MpSellerShipRateCollection
     */
    protected $_mpSellerShipRateCollectionFactory;

    /**
     * @var array
     */
    protected $codes = [
        'code' => [
            'label' => 'Code',
            'class' => 'validate-no-empty validate-data',
            'default' => ''
        ],
        'title' => [
            'label' => 'Title',
            'class' => 'validate-no-empty',
            'default' => ''
        ],
        'price' => [
            'label' => 'Price',
            'class' => 'validate-no-empty greater-than-equals-to-0',
            'default' => ''
        ],
        'sort_order' => [
            'label' => 'Admin Sort',
            'class' => 'validate-no-empty greater-than-equals-to-0',
            'default' => 99
        ]
    ];

    /**
     * Excluded Keys While Validation Category Data
     *
     * @var array
     */
    private $excludes = ["product_ids", "form_key"];

    /**
     * @var array
     */
    protected $headerTemplate;
    protected ManagerFactory $_cacheManager;
    protected DataAlias $_mpHelper;

    public function __construct(
        Context $context,
        protected EncryptorInterface $encryptor,
        protected WriterInterface $configWriter,
        protected ClientFactory $clientFactory,
        DataAlias $mpHelper,
        MpSellerShipRateCollection $mpSellerShipRateCollectionFactory,
        ManagerFactory $cacheManagerFactory,
    ) {
        $this->_mpHelper = $mpHelper;
        $this->_mpSellerShipRateCollectionFactory = $mpSellerShipRateCollectionFactory;
        $this->_cacheManager = $cacheManagerFactory;
        parent::__construct($context);
    }

    /**
     * @param null $storeId
     * @return array|mixed
     */
    public function getShippingType($storeId = null)
    {
        if (!$this->shippingType) {
            $arrayValues = [];
            $configData = $this->getConfigData('shipping_type', $storeId);

            if (is_string($configData) && !empty($configData) && $configData !== '[]') {
                if ($this->isJson($configData)) {
                    $arrayValues = (array) json_decode($configData, true);
                } else {
                    $arrayValues = (array) array_values(unserialize($configData));
                }
            }

            $arrayValues = $this->shippingArrayObject($arrayValues);

            usort($arrayValues, function ($a, $b) {
                if (array_key_exists('sort_order', $a)) {
                    return $a['sort_order'] - $b['sort_order'];
                } else {
                    return 0;
                }
            });

            $this->shippingType = $arrayValues;
        }

        return $this->shippingType;
    }

    /**
     * input {code}_{method}
     * return method
     * @param $method_code
     * @param null $storeId
     * @return string
     */
    public function getShippingCodeFromMethod($method_code, $storeId = null)
    {
        $result = '';

        foreach ($this->getShippingType($storeId) as $shippingType) {
            if (Carrier::CODE . '_' . $shippingType['code'] == $method_code) {
                $result = $shippingType['code'];
                break;
            }
        }

        return $result;
    }

    /**
     * @param  null|string $storeId
     * @return bool
     */
    public function isEnabled($storeId = null)
    {
        return $this->scopeConfig->isSetFlag(
            'carriers/' . Carrier::CODE . '/active',
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Retrieve information from carrier configuration
     *
     * @param string $field
     * @param null $storeId
     * @return  string
     */
    public function getConfigData($field, $storeId = null)
    {
        return $this->scopeConfig->getValue(
            'carriers/' . Carrier::CODE . '/' . $field,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Check whether seller TableRates are allowed or not
     *
     * @return boolean
     */
    public function isAllowedSellerTableRates($storeId = null)
    {
        return $this->scopeConfig->getValue(
            'coditron_customshippingrate/about_coditron/active',
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Clean Cache
     */
    public function clearCache()
    {
        $cacheManager = $this->_cacheManager->create();
        $availableTypes = $cacheManager->getAvailableTypes();
        $cacheManager->clean($availableTypes);
    }

    /**
     * Get Seller Id
     *
     * @return integer
     */
    public function getSellerId()
    {
        return (int) $this->_mpHelper->getCustomerId();
    }

    /**
     * Return the Customer seller status.
     *
     * @param int $sellerId
     * @return bool|0|1
     */
    public function isSeller($sellerId = 0)
    {
        $sellerStatus = 0;
        $model = $this->_mpHelper->getSellerCollectionObj($sellerId);
        foreach ($model as $value) {
            if ($value->getIsSeller() == 1) {
                $sellerStatus = $value->getIsSeller();
            }
        }
        return $sellerStatus;
    }

    /**
     * Check whether courier exist or not
     *
     * @param string $courierName
     * @param integer $sellerId
     * @param integer $rateId
     *
     * @return boolean
     */
    public function isExistingCategory($courierName, $sellerId = 0, $rateId = 0)
    {
        if (!$rateId) {
            $rateId = $this->_request->getParam("id");
        }

        if (!$sellerId) {
            $sellerId = $this->getSellerId();
        }

        $courierName = trim($courierName);
        $collection = $this->_mpSellerShipRateCollectionFactory->create();
        $collection->addFieldToFilter("courier_name", ["eq" => $courierName]);
        $collection->addFieldToFilter("seller_id", ["eq" => $sellerId]);

        if ($rateId) {
            $collection->addFieldToFilter("shiptablerates_id", ["neq" => $rateId]);
        }

        if ($collection->getSize()) {
            return true;
        }

        return false;
    }

    /**
     * Check if free shipping threshold already exists for any of the selected countries
     *
     * @param array $countries
     * @param int $sellerId
     * @param mixed $rateId
     * @return array Array of countries that already have thresholds
     */
    public function getExistingThresholdCountries(array $countries, int $sellerId = 0, $rateId = 0): array
    {
        // Ensure rateId is properly cast to integer
        $rateId = (int) $rateId;

        if (!$rateId) {
            $rateId = (int) $this->_request->getParam("id");
        }

        if (!$sellerId) {
            $sellerId = $this->getSellerId();
        }

        $existingCountries = [];

        foreach ($countries as $country) {
            $country = trim($country);
            if (empty($country)) {
                continue;
            }

            $collection = $this->_mpSellerShipRateCollectionFactory->create();
            $collection->addFieldToFilter("seller_id", ["eq" => $sellerId])
                      ->addFieldToFilter("free_shipping", ["eq" => 1])
                      ->addFieldToFilter("min_order_amount", ["gt" => 0])
                      ->addFieldToFilter("countries", ["like" => "%{$country}%"]);

            // Exclude current record when editing
            if ($rateId) {
                $collection->addFieldToFilter("shiptablerates_id", ["neq" => $rateId]);
            }

            if ($collection->getSize() > 0) {
                $existingCountries[] = $country;
            }
        }

        return $existingCountries;
    }

    /**
     * Validate Category Data
     *
     * @param array $details
     * @param boolean $validateSellerId
     *
     * @return array
     */
    public function validateData(&$details, $validateSellerId = false)
    {
        $result = ["error" => false, "msg" => ""];
        $this->removeSpaces($details);
        if (empty($details['courier_name'])) {
            $result["error"] = true;
            $result["msg"] = "courier name is required field.";
            return $result;
        }

        if (!isset($details['countries'])) {
            $result["error"] = true;
            $result["msg"] = "countries is required field.";
            return $result;
        }

        if (!isset($details['weight'])) {
            $result["error"] = true;
            $result["msg"] = "weight is required field.";
            return $result;
        }

        if ($validateSellerId && empty($details['seller_id'])) {
            $result["error"] = true;
            $result["msg"] = "Seller is required field.";
            return $result;
        }

        if ($validateSellerId && empty($details['shipping_price'])) {
            $result["error"] = true;
            $result["msg"] = "shipping price is required field.";
            return $result;
        }

        return $result;
    }

    /**
     * Remove Spaces and Special Characters from Array Values
     *
     * @param array $details
     */
    public function removeSpaces(&$details)
    {
        foreach ($details as $key => $value) {
            if (in_array($key, $this->excludes)) {
                continue;
            }
            if (is_numeric($value) && floor(floatval($value)) != $value) {
                $details[$key] = floatval($value);
            } else {
                $value = is_string($value) ? trim($this->removeSpecialCharacters($value)) : $value;
                $details[$key] = $value;
            }
        }
    }

    /**
     * Remove Special Characters from String
     *
     * @param string $string
     *
     * @return string
     */
    public function removeSpecialCharacters($string)
    {
        return preg_replace('/[^A-Za-z0-9\-\ ]/', '', $string);
    }

    /**
     * @param  null|string $storeId
     * @return bool
     */
    public function getTsqToken($storeId = null)
    {
        return $this->scopeConfig->getValue(
            'carriers/' . Carrier::CODE . '/tsq_token',
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * Magento 2.2 return json instead of serialize array
     *
     * @param   string $string
     * @return  bool
     */
    public function isJson($string)
    {
        json_decode($string);
        return (json_last_error() == JSON_ERROR_NONE);
    }

    /**
     * @return array
     */
    public function getHeaderTemplate()
    {
        if (!$this->headerTemplate) {
            $this->headerTemplate = [];

            foreach ($this->getHeaderColumns() as $key => $column) {
                $this->headerTemplate[$key] = $column['default'];
            }
        }

        return $this->headerTemplate;
    }

    /**
     * @return array
     */
    public function getHeaderColumns()
    {
        return $this->codes;
    }

    /**
     * @param $values
     * @return mixed
     */
    public function shippingArrayObject($values)
    {
        //fix existing options
        $requiredFields = $this->getHeaderTemplate();

        if (is_array($values)) {
            foreach ($values as &$row) {
                $row = array_merge($requiredFields, $row);
            }
        }

        return $values;
    }
}
