<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_MpSellerCategory
 * <AUTHOR> Software Private Limited
 * @copyright Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
namespace Coditron\CustomShippingRate\Controller\ShipTableRates;

class Save extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
{
    /**
     * Seller Shipping Rate Save action
     * Handles both regular shipping methods and free shipping thresholds
     *
     * @return \Magento\Framework\Controller\Result\RedirectFactory
     */
    public function execute()
    {
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        if ($this->getRequest()->isPost()) {
            try {
                if (!$this->_formKeyValidator->validate($this->getRequest())) {
                    return $this->resultRedirectFactory->create()->setPath(
                        'coditron_customshippingrate/shiptablerates/manage',
                        ['_secure' => $this->getRequest()->isSecure()]
                    );
                }

                $postData = $this->getRequest()->getPostValue();
                $postData['seller_id'] = $this->getSellerId();

                // Validate basic data
                $result = $this->_helper->validateData($postData);
                if ($result['error']) {
                    $this->messageManager->addError(__($result['msg']));
                    return $this->resultRedirectFactory->create()->setPath(
                        'coditron_customshippingrate/shiptablerates/manage',
                        ['_secure' => $this->getRequest()->isSecure()]
                    );
                }

                // Additional validation for free shipping threshold duplicates
                $validationResult = $this->validateFreeShippingThresholds($postData);
                if ($validationResult['error']) {
                    $this->messageManager->addError(__($validationResult['msg']));
                    return $this->resultRedirectFactory->create()->setPath(
                        'coditron_customshippingrate/shiptablerates/manage',
                        ['_secure' => $this->getRequest()->isSecure()]
                    );
                }

                $sellerShiprate = $this->getSellerShiprate();


                if ($postData['id']) {
                    $sellerShiprate->addData($postData)->setShiptableratesId($postData['id']);
                } else {
                    $sellerShiprate->setData($postData);
                }

                $sellerShiprate->save();
                $id = $sellerShiprate->getShiptableratesId();

                $isThreshold = $this->getRequest()->getParam('is_threshold', false);
                $successMessage = $isThreshold ? "Free Shipping Threshold saved successfully." : "Shipping Rate saved successfully.";
                $this->messageManager->addSuccess(__($successMessage));
                $this->_helper->clearCache();

                $params = ['shiptablerates_id' => $id, '_secure' => $this->getRequest()->isSecure()];
                if ($isThreshold) {
                    $params['is_threshold'] = 1;
                }

                return $this->resultRedirectFactory->create()->setPath(
                    'coditron_customshippingrate/shiptablerates/edit',
                    $params
                );
            } catch (\Exception $e) {
                $this->messageManager->addError($e->getMessage());
                return $this->resultRedirectFactory->create()->setPath(
                    'coditron_customshippingrate/shiptablerates/manage',
                    ['_secure' => $this->getRequest()->isSecure()]
                );
            }
        } else {
            return $this->resultRedirectFactory->create()->setPath(
                'coditron_customshippingrate/shiptablerates/manage',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }
    }

    /**
     * Validate free shipping threshold duplicates
     *
     * @param array $postData
     * @return array
     */
    private function validateFreeShippingThresholds(array $postData): array
    {
        $result = ["error" => false, "msg" => ""];

        // Check if this is a free shipping threshold
        $isThreshold = isset($postData['is_threshold']) && $postData['is_threshold'];
        $isFreeShipping = isset($postData['free_shipping']) && $postData['free_shipping'];
        $hasMinAmount = isset($postData['min_order_amount']) && $postData['min_order_amount'] > 0;

        if ($isThreshold && $isFreeShipping && $hasMinAmount) {
            $countries = is_array($postData['countries']) ? $postData['countries'] : explode(',', $postData['countries']);
            $sellerId = (int) ($postData['seller_id'] ?? 0);
            $rateId = (int) ($postData['id'] ?? 0);

            $existingCountries = $this->_helper->getExistingThresholdCountries($countries, $sellerId, $rateId);

            if (!empty($existingCountries)) {
                $result["error"] = true;
                $countryList = implode(', ', $existingCountries);
                $result["msg"] = "Free shipping threshold already exists for the following countries: {$countryList}. You can only edit existing thresholds.";
            }
        }

        return $result;
    }
}
